# 个性化膳食营养分析平台开发文档

## 1. 项目架构设计

### 1.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue3)    │    │   网关 (Gateway) │    │  第三方服务      │
│   - 用户界面     │◄──►│   - 路由转发     │◄──►│  - 图像识别API   │
│   - 状态管理     │    │   - 认证鉴权     │    │  - 支付接口      │
│   - 组件库       │    │   - 限流熔断     │    │  - 消息推送      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │
         ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CDN/静态资源   │    │   后端服务集群    │    │   数据存储层     │
│   - 图片资源     │    │   - 用户服务     │    │   - MySQL       │
│   - 静态文件     │    │   - 食物服务     │    │   - Redis       │
│   - 缓存加速     │    │   - 分析服务     │    │   - 文件存储     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 技术栈选型

#### 后端技术栈
- **框架**: Spring Boot 2.7.x
- **安全**: Spring Security + JWT
- **数据访问**: MyBatis Plus
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.x
- **消息队列**: RabbitMQ
- **文档**: Swagger 3.x
- **监控**: Spring Boot Actuator + Micrometer

#### 前端技术栈
- **框架**: Vue 3.x + TypeScript
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4.x
- **HTTP客户端**: Axios
- **构建工具**: Vite
- **图表库**: ECharts

#### 开发工具
- **IDE**: IntelliJ IDEA / VS Code
- **版本控制**: Git
- **项目管理**: Maven
- **容器化**: Docker + Docker Compose
- **CI/CD**: Jenkins / GitHub Actions

## 2. 数据库设计

### 2.1 核心表结构

#### 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone)
) COMMENT '用户基本信息表';
```

#### 用户健康档案表 (user_profiles)
```sql
CREATE TABLE user_profiles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    gender TINYINT COMMENT '性别：1-男，2-女',
    birth_date DATE COMMENT '出生日期',
    height DECIMAL(5,2) COMMENT '身高(cm)',
    weight DECIMAL(5,2) COMMENT '体重(kg)',
    activity_level TINYINT COMMENT '活动水平：1-久坐，2-轻度，3-中度，4-重度',
    health_conditions JSON COMMENT '健康状况',
    allergies JSON COMMENT '过敏信息',
    dietary_preferences JSON COMMENT '饮食偏好',
    target_weight DECIMAL(5,2) COMMENT '目标体重',
    target_calories INT COMMENT '目标热量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id)
) COMMENT '用户健康档案表';
```

#### 食物信息表 (foods)
```sql
CREATE TABLE foods (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '食物名称',
    category_id INT COMMENT '分类ID',
    brand VARCHAR(100) COMMENT '品牌',
    barcode VARCHAR(50) COMMENT '条形码',
    image_url VARCHAR(255) COMMENT '图片URL',
    serving_size DECIMAL(8,2) COMMENT '标准份量(g)',
    calories_per_100g DECIMAL(8,2) COMMENT '每100g热量(kcal)',
    protein_per_100g DECIMAL(8,2) COMMENT '每100g蛋白质(g)',
    fat_per_100g DECIMAL(8,2) COMMENT '每100g脂肪(g)',
    carbs_per_100g DECIMAL(8,2) COMMENT '每100g碳水化合物(g)',
    fiber_per_100g DECIMAL(8,2) COMMENT '每100g纤维(g)',
    sugar_per_100g DECIMAL(8,2) COMMENT '每100g糖(g)',
    sodium_per_100g DECIMAL(8,2) COMMENT '每100g钠(mg)',
    vitamins JSON COMMENT '维生素含量',
    minerals JSON COMMENT '矿物质含量',
    gi_value TINYINT COMMENT 'GI值',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-下架',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_category (category_id),
    INDEX idx_barcode (barcode)
) COMMENT '食物信息表';
```

#### 饮食记录表 (diet_records)
```sql
CREATE TABLE diet_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    food_id BIGINT NOT NULL COMMENT '食物ID',
    meal_type TINYINT NOT NULL COMMENT '餐次：1-早餐，2-午餐，3-晚餐，4-加餐',
    amount DECIMAL(8,2) NOT NULL COMMENT '摄入量(g)',
    record_date DATE NOT NULL COMMENT '记录日期',
    record_time TIME COMMENT '记录时间',
    calories DECIMAL(8,2) COMMENT '热量(kcal)',
    protein DECIMAL(8,2) COMMENT '蛋白质(g)',
    fat DECIMAL(8,2) COMMENT '脂肪(g)',
    carbs DECIMAL(8,2) COMMENT '碳水化合物(g)',
    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (food_id) REFERENCES foods(id),
    INDEX idx_user_date (user_id, record_date),
    INDEX idx_user_meal (user_id, meal_type)
) COMMENT '饮食记录表';
```

### 2.2 索引优化策略
- **复合索引**: 根据查询模式创建复合索引
- **分区表**: 对大数据量表按时间分区
- **读写分离**: 主从复制，读写分离
- **缓存策略**: 热点数据Redis缓存

## 3. 后端开发规范

### 3.1 项目结构
```
nutrition-platform/
├── nutrition-common/          # 公共模块
│   ├── src/main/java/
│   │   └── com/nutrition/common/
│   │       ├── config/        # 配置类
│   │       ├── constant/      # 常量定义
│   │       ├── exception/     # 异常处理
│   │       ├── utils/         # 工具类
│   │       └── vo/           # 通用VO
├── nutrition-user/           # 用户服务
│   ├── src/main/java/
│   │   └── com/nutrition/user/
│   │       ├── controller/    # 控制器
│   │       ├── service/       # 业务逻辑
│   │       ├── mapper/        # 数据访问
│   │       ├── entity/        # 实体类
│   │       └── dto/          # 数据传输对象
├── nutrition-food/           # 食物服务
├── nutrition-analysis/       # 分析服务
├── nutrition-gateway/        # 网关服务
└── nutrition-admin/          # 管理后台
```

### 3.2 编码规范

#### 命名规范
```java
// 类名：大驼峰命名
public class UserService {}

// 方法名：小驼峰命名
public void getUserById() {}

// 常量：全大写，下划线分隔
public static final String DEFAULT_ENCODING = "UTF-8";

// 包名：全小写，点分隔
package com.nutrition.user.service;
```

#### 注释规范
```java
/**
 * 用户服务类
 * 提供用户相关的业务逻辑处理
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Service
public class UserService {
    
    /**
     * 根据用户ID获取用户信息
     * 
     * @param userId 用户ID，不能为空
     * @return 用户信息，如果用户不存在返回null
     * @throws IllegalArgumentException 当userId为空时抛出
     */
    public User getUserById(Long userId) {
        // 实现逻辑
    }
}
```

### 3.3 API设计规范

#### RESTful API设计
```java
@RestController
@RequestMapping("/api/v1/users")
@Api(tags = "用户管理")
public class UserController {
    
    @GetMapping("/{id}")
    @ApiOperation("获取用户信息")
    public Result<UserVO> getUser(@PathVariable Long id) {
        // 实现逻辑
    }
    
    @PostMapping
    @ApiOperation("创建用户")
    public Result<Long> createUser(@RequestBody @Valid CreateUserDTO dto) {
        // 实现逻辑
    }
    
    @PutMapping("/{id}")
    @ApiOperation("更新用户信息")
    public Result<Void> updateUser(@PathVariable Long id, 
                                  @RequestBody @Valid UpdateUserDTO dto) {
        // 实现逻辑
    }
    
    @DeleteMapping("/{id}")
    @ApiOperation("删除用户")
    public Result<Void> deleteUser(@PathVariable Long id) {
        // 实现逻辑
    }
}
```

#### 统一响应格式
```java
@Data
public class Result<T> {
    private Integer code;
    private String message;
    private T data;
    private Long timestamp;
    
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setMessage("success");
        result.setData(data);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }
    
    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        result.setTimestamp(System.currentTimeMillis());
        return result;
    }
}
```

## 4. 前端开发规范

### 4.1 项目结构
```
nutrition-web/
├── public/                   # 静态资源
├── src/
│   ├── api/                 # API接口
│   ├── assets/              # 资源文件
│   ├── components/          # 公共组件
│   ├── composables/         # 组合式函数
│   ├── layouts/             # 布局组件
│   ├── pages/               # 页面组件
│   ├── router/              # 路由配置
│   ├── stores/              # 状态管理
│   ├── styles/              # 样式文件
│   ├── types/               # 类型定义
│   ├── utils/               # 工具函数
│   ├── App.vue              # 根组件
│   └── main.ts              # 入口文件
├── package.json
├── vite.config.ts
└── tsconfig.json
```

### 4.2 组件开发规范

#### 组件命名
```typescript
// 组件文件名：大驼峰命名
UserProfile.vue
FoodSearch.vue
NutritionChart.vue

// 组件注册名：短横线命名
<user-profile />
<food-search />
<nutrition-chart />
```

#### 组件结构
```vue
<template>
  <div class="user-profile">
    <!-- 模板内容 -->
  </div>
</template>

<script setup lang="ts">
// 导入依赖
import { ref, computed, onMounted } from 'vue'
import type { User } from '@/types/user'

// 定义Props
interface Props {
  userId: number
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

// 定义Emits
interface Emits {
  (e: 'update', user: User): void
  (e: 'delete', id: number): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const user = ref<User | null>(null)
const loading = ref(false)

// 计算属性
const displayName = computed(() => {
  return user.value?.nickname || user.value?.username || '未知用户'
})

// 方法
const loadUser = async () => {
  loading.value = true
  try {
    // 加载用户数据
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadUser()
})
</script>

<style scoped>
.user-profile {
  /* 样式定义 */
}
</style>
```

### 4.3 状态管理
```typescript
// stores/user.ts
import { defineStore } from 'pinia'
import type { User } from '@/types/user'
import { userApi } from '@/api/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref<User | null>(null)
  const isLoggedIn = ref(false)
  
  // 计算属性
  const userProfile = computed(() => {
    return currentUser.value?.profile
  })
  
  // 方法
  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await userApi.login(credentials)
      currentUser.value = response.data.user
      isLoggedIn.value = true
      // 保存token
      localStorage.setItem('token', response.data.token)
    } catch (error) {
      throw error
    }
  }
  
  const logout = () => {
    currentUser.value = null
    isLoggedIn.value = false
    localStorage.removeItem('token')
  }
  
  return {
    currentUser,
    isLoggedIn,
    userProfile,
    login,
    logout
  }
})
```

## 5. 部署配置

### 5.1 Docker配置

#### 后端Dockerfile
```dockerfile
FROM openjdk:11-jre-slim

WORKDIR /app

COPY target/nutrition-platform.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

#### 前端Dockerfile
```dockerfile
FROM node:16-alpine as build

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine

COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### 5.2 Docker Compose配置
```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: nutrition_db
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

  backend:
    build: ./nutrition-backend
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    environment:
      SPRING_PROFILES_ACTIVE: prod

  frontend:
    build: ./nutrition-frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  mysql_data:
```

## 6. 开发流程

### 6.1 Git工作流
```bash
# 功能开发流程
git checkout -b feature/user-management
# 开发功能
git add .
git commit -m "feat: 添加用户管理功能"
git push origin feature/user-management
# 创建Pull Request

# 提交信息规范
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 6.2 代码审查清单
- [ ] 代码符合编码规范
- [ ] 单元测试覆盖率 > 80%
- [ ] API文档已更新
- [ ] 性能影响评估
- [ ] 安全性检查
- [ ] 数据库变更脚本

### 6.3 发布流程
1. **开发环境测试**: 功能测试、单元测试
2. **测试环境部署**: 集成测试、性能测试
3. **预生产验证**: 用户验收测试
4. **生产环境发布**: 灰度发布、全量发布
5. **监控告警**: 实时监控、异常告警

## 7. 测试策略

### 7.1 测试分层
- **单元测试**: JUnit 5 + Mockito
- **集成测试**: Spring Boot Test
- **接口测试**: Postman + Newman
- **前端测试**: Vitest + Vue Test Utils
- **E2E测试**: Cypress

### 7.2 测试覆盖率要求
- 单元测试覆盖率 > 80%
- 集成测试覆盖率 > 60%
- 核心业务逻辑覆盖率 > 90%

### 7.3 测试示例

#### 后端单元测试
```java
@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock
    private UserMapper userMapper;

    @InjectMocks
    private UserService userService;

    @Test
    @DisplayName("根据ID获取用户信息 - 成功")
    void getUserById_Success() {
        // Given
        Long userId = 1L;
        User mockUser = new User();
        mockUser.setId(userId);
        mockUser.setUsername("testuser");

        when(userMapper.selectById(userId)).thenReturn(mockUser);

        // When
        User result = userService.getUserById(userId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(userId);
        assertThat(result.getUsername()).isEqualTo("testuser");
        verify(userMapper).selectById(userId);
    }

    @Test
    @DisplayName("根据ID获取用户信息 - 用户不存在")
    void getUserById_UserNotFound() {
        // Given
        Long userId = 999L;
        when(userMapper.selectById(userId)).thenReturn(null);

        // When & Then
        assertThatThrownBy(() -> userService.getUserById(userId))
            .isInstanceOf(UserNotFoundException.class)
            .hasMessage("用户不存在");
    }
}
```

#### 前端组件测试
```typescript
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import UserProfile from '@/components/UserProfile.vue'

describe('UserProfile', () => {
  it('应该正确渲染用户信息', () => {
    const user = {
      id: 1,
      username: 'testuser',
      nickname: '测试用户'
    }

    const wrapper = mount(UserProfile, {
      props: { user }
    })

    expect(wrapper.text()).toContain('测试用户')
    expect(wrapper.find('.username').text()).toBe('testuser')
  })

  it('应该在点击编辑按钮时触发编辑事件', async () => {
    const wrapper = mount(UserProfile, {
      props: { user: { id: 1, username: 'test' } }
    })

    await wrapper.find('.edit-btn').trigger('click')

    expect(wrapper.emitted('edit')).toBeTruthy()
    expect(wrapper.emitted('edit')[0]).toEqual([1])
  })
})
```

## 8. 性能优化

### 8.1 后端性能优化

#### 数据库优化
```sql
-- 创建复合索引
CREATE INDEX idx_diet_records_user_date ON diet_records(user_id, record_date);

-- 分区表设计
CREATE TABLE diet_records_2024 PARTITION OF diet_records
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

-- 查询优化
EXPLAIN ANALYZE
SELECT * FROM diet_records
WHERE user_id = 1 AND record_date BETWEEN '2024-01-01' AND '2024-01-31';
```

#### 缓存策略
```java
@Service
public class FoodService {

    @Cacheable(value = "foods", key = "#id")
    public Food getFoodById(Long id) {
        return foodMapper.selectById(id);
    }

    @CacheEvict(value = "foods", key = "#food.id")
    public void updateFood(Food food) {
        foodMapper.updateById(food);
    }

    @Cacheable(value = "food_search", key = "#keyword")
    public List<Food> searchFoods(String keyword) {
        return foodMapper.searchByKeyword(keyword);
    }
}
```

#### 异步处理
```java
@Service
public class NutritionAnalysisService {

    @Async("taskExecutor")
    public CompletableFuture<AnalysisResult> analyzeNutrition(Long userId, Date date) {
        // 执行营养分析计算
        AnalysisResult result = performAnalysis(userId, date);
        return CompletableFuture.completedFuture(result);
    }

    @EventListener
    @Async
    public void handleDietRecordCreated(DietRecordCreatedEvent event) {
        // 异步更新营养统计
        updateNutritionStats(event.getUserId(), event.getRecordDate());
    }
}
```

### 8.2 前端性能优化

#### 组件懒加载
```typescript
// router/index.ts
const routes = [
  {
    path: '/dashboard',
    component: () => import('@/pages/Dashboard.vue')
  },
  {
    path: '/analysis',
    component: () => import('@/pages/NutritionAnalysis.vue')
  }
]
```

#### 虚拟滚动
```vue
<template>
  <div class="food-list">
    <RecycleScroller
      class="scroller"
      :items="foods"
      :item-size="60"
      key-field="id"
      v-slot="{ item }"
    >
      <FoodItem :food="item" />
    </RecycleScroller>
  </div>
</template>
```

#### 图片懒加载
```vue
<template>
  <img
    v-lazy="food.imageUrl"
    :alt="food.name"
    class="food-image"
  />
</template>
```

## 9. 安全设计

### 9.1 认证授权
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf().disable()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/v1/auth/**").permitAll()
                .requestMatchers("/api/v1/admin/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .addFilterBefore(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
```

### 9.2 数据加密
```java
@Component
public class EncryptionUtil {

    @Value("${app.encryption.key}")
    private String encryptionKey;

    public String encrypt(String plainText) {
        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(encryptionKey.getBytes(), "AES");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);

            byte[] encryptedData = cipher.doFinal(plainText.getBytes());
            return Base64.getEncoder().encodeToString(encryptedData);
        } catch (Exception e) {
            throw new EncryptionException("加密失败", e);
        }
    }
}
```

### 9.3 输入验证
```java
@RestController
public class UserController {

    @PostMapping("/users")
    public Result<Long> createUser(@RequestBody @Valid CreateUserDTO dto) {
        // 额外的业务验证
        if (userService.existsByUsername(dto.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        // XSS防护
        String sanitizedNickname = HtmlUtils.htmlEscape(dto.getNickname());
        dto.setNickname(sanitizedNickname);

        return Result.success(userService.createUser(dto));
    }
}
```

## 10. 监控运维

### 10.1 应用监控
```yaml
# application.yml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

### 10.2 日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/nutrition-platform.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>logs/nutrition-platform.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>

        <root level="INFO">
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
</configuration>
```

### 10.3 健康检查
```java
@Component
public class DatabaseHealthIndicator implements HealthIndicator {

    @Autowired
    private DataSource dataSource;

    @Override
    public Health health() {
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(1)) {
                return Health.up()
                    .withDetail("database", "MySQL")
                    .withDetail("status", "UP")
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("database", "MySQL")
                .withDetail("error", e.getMessage())
                .build();
        }

        return Health.down().build();
    }
}
```

## 11. 开发环境搭建

### 11.1 环境要求
- **JDK**: 11+
- **Node.js**: 16+
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Maven**: 3.6+
- **Git**: 2.0+

### 11.2 快速启动
```bash
# 1. 克隆项目
git clone https://github.com/your-org/nutrition-platform.git
cd nutrition-platform

# 2. 启动基础服务
docker-compose up -d mysql redis

# 3. 初始化数据库
mysql -h localhost -u root -p < scripts/init.sql

# 4. 启动后端服务
cd nutrition-backend
mvn spring-boot:run

# 5. 启动前端服务
cd nutrition-frontend
npm install
npm run dev
```

### 11.3 开发工具配置

#### IDEA配置
```xml
<!-- .idea/codeStyles/Project.xml -->
<code_scheme name="Project" version="173">
  <JavaCodeStyleSettings>
    <option name="IMPORT_LAYOUT_TABLE">
      <value>
        <package name="java" withSubpackages="true" static="false"/>
        <package name="javax" withSubpackages="true" static="false"/>
        <package name="org" withSubpackages="true" static="false"/>
        <package name="com" withSubpackages="true" static="false"/>
        <package name="" withSubpackages="true" static="false"/>
        <package name="" withSubpackages="true" static="true"/>
      </value>
    </option>
  </JavaCodeStyleSettings>
</code_scheme>
```

#### VS Code配置
```json
// .vscode/settings.json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "vue.codeActions.enabled": true
}
```

## 12. 常见问题解决

### 12.1 数据库连接问题
```yaml
# 解决方案：检查数据库配置
spring:
  datasource:
    url: ***************************************************************************************************************
    username: root
    password: root123
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 12.2 跨域问题
```java
@Configuration
public class CorsConfig {

    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        config.addAllowedOriginPattern("*");
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);

        return new CorsFilter(source);
    }
}
```

### 12.3 内存溢出问题
```bash
# JVM参数调优
java -Xms512m -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -jar app.jar
```

---

**文档版本**: v1.0
**最后更新**: 2024-01-01
**维护人员**: 开发团队
